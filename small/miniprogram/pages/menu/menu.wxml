<!--菜单页-->
<view class="menu-container">
  <!-- 桌号显示 -->
  <view class="table-info">
    <text class="table-text">{{tableNumber}}号桌</text>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 分类导航 -->
    <view class="category-sidebar">
      <view class="category-list">
        <view
          class="category-item {{currentCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          bindtap="switchCategory"
          data-id="{{item.id}}"
        >
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 菜品列表 -->
    <view class="dishes-container">
      <scroll-view class="dishes-scroll" scroll-y="true">
        <view class="dishes-list">
          <view
            class="dish-item"
            wx:for="{{currentDishes}}"
            wx:key="id"
          >
            <view class="dish-image">
              <text class="dish-emoji">{{item.emoji}}</text>
            </view>
            <view class="dish-info">
              <view class="dish-name-row">
                <text class="dish-name">{{item.name}}</text>
                <text class="recommend-tag" wx:if="{{item.isRecommended}}">推荐</text>
              </view>
              <text class="dish-desc">{{item.description}}</text>
              <view class="dish-bottom">
                <text class="dish-price price">¥{{item.price}}</text>
                <view class="dish-controls">
                  <view
                    class="control-btn minus {{item.count > 0 ? '' : 'disabled'}}"
                    bindtap="decreaseDish"
                    data-id="{{item.id}}"
                  >-</view>
                  <text class="dish-count">{{item.count || 0}}</text>
                  <view
                    class="control-btn plus"
                    bindtap="increaseDish"
                    data-id="{{item.id}}"
                  >+</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 购物车浮层 -->
  <view class="cart-float {{cartItems.length > 0 ? 'show' : ''}}" bindtap="toggleCart">
    <view class="cart-info">
      <view class="cart-icon-wrapper">
        <text class="cart-icon">🛒</text>
        <view class="cart-badge" wx:if="{{totalCount > 0}}">{{totalCount}}</view>
      </view>
      <view class="cart-text">
        <text class="cart-count">已选{{totalCount}}件</text>
        <text class="cart-price price">¥{{totalPrice}}</text>
      </view>
    </view>
    <view class="checkout-btn btn btn-primary" bindtap="checkout">
      去结算
    </view>
  </view>

  <!-- 购物车详情弹窗 -->
  <view class="cart-modal {{showCartModal ? 'show' : ''}}" bindtap="hideCartModal">
    <view class="cart-content" catchtap="stopPropagation">
      <view class="cart-header">
        <text class="cart-title">购物车</text>
        <view class="clear-cart" bindtap="clearCart">清空</view>
      </view>
      <scroll-view class="cart-list" scroll-y="true">
        <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
          <view class="cart-item-image">
            <text class="dish-emoji">{{item.emoji}}</text>
          </view>
          <view class="cart-item-info">
            <text class="cart-item-name">{{item.name}}</text>
            <text class="cart-item-price price">¥{{item.price}}</text>
          </view>
          <view class="cart-item-controls">
            <view 
              class="control-btn minus"
              bindtap="decreaseDish"
              data-id="{{item.id}}"
            >-</view>
            <text class="cart-item-count">{{item.count}}</text>
            <view 
              class="control-btn plus"
              bindtap="increaseDish"
              data-id="{{item.id}}"
            >+</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</view>

<!-- 自定义底部导航栏 -->
<gradient-tabbar
  current-tab="{{1}}"
  theme="glass"
  bind:tabchange="onTabChange"
></gradient-tabbar>
