<!--首页-->
<view class="home-container">

  <!-- 桌号选择 -->
  <view class="table-selection card">
    <view class="section-title">
      <text class="title">选择桌号</text>
    </view>
    
    <!-- 桌号选择 -->
    <view class="table-grid">
      <view
        class="table-item {{tableNumber === item.number ? 'selected' : ''}} {{item.occupied ? 'occupied' : ''}}"
        wx:for="{{tableList}}"
        wx:key="number"
        bindtap="selectTable"
        data-table="{{item.number}}"
        data-occupied="{{item.occupied}}"
      >
        <text class="table-number">{{item.number}}</text>
        <text class="table-status" wx:if="{{item.occupied}}">已占用</text>
      </view>
    </view>

    <!-- 手动输入 -->
    <!-- <view class="manual-input-section">
      <text class="input-label">其他桌号:</text>
      <input
        class="table-number-input"
        type="number"
        placeholder="请输入桌号"
        value="{{manualTableNumber}}"
        bindinput="onManualTableInput"
        bindconfirm="confirmManualInput"
      />
      <button class="confirm-btn" bindtap="confirmManualInput">确认</button>
    </view> -->

    <!-- 开始点餐按钮 -->
    <button
      class="start-order-btn btn btn-primary"
      bindtap="startOrder"
      disabled="{{tableNumber === 0}}"
    >
      开始点餐
    </button>
  </view>

  <!-- 今日推荐 -->
  <view class="today-recommend card">
    <view class="section-title">
      <text class="title">今日推荐</text>
    </view>
    <view class="recommend-list">
      <view class="recommend-item" wx:for="{{recommendList}}" wx:key="id">
        <view class="recommend-image">
          <text class="dish-emoji">{{item.emoji}}</text>
        </view>
        <view class="recommend-info">
          <text class="recommend-name">{{item.name}}</text>
          <text class="recommend-price price">¥{{item.price}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 店铺信息 -->
  <view class="shop-details card">
    <view class="detail-item">
      <view class="detail-icon">⏰</view>
      <text class="detail-text">营业时间: 09:00 - 22:00</text>
    </view>
    <view class="detail-item">
      <view class="detail-icon">📞</view>
      <text class="detail-text">联系电话: 123-4567-8890</text>
    </view>
    <view class="detail-item">
      <view class="detail-icon">📍</view>
      <text class="detail-text">地址: 美食街88号</text>
    </view>
  </view>
</view>

<!-- 自定义底部导航栏 -->
<gradient-tabbar
  current-tab="{{0}}"
  theme="glass"
  bind:tabchange="onTabChange"
></gradient-tabbar>
