/* 彩色渐变底部导航栏样式 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 主体样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    padding-bottom: 70px; /* 为底部导航留出空间 */
}

/* 底部导航栏容器 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 1000;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 样式主题 1: 经典蓝紫渐变 */
.bottom-nav.theme-classic {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
    box-shadow: 0 -8px 32px rgba(102, 126, 234, 0.4);
}

/* 样式主题 2: 彩虹渐变 */
.bottom-nav.theme-rainbow {
    background: linear-gradient(45deg, 
        #ff6b6b 0%, 
        #4ecdc4 20%, 
        #45b7d1 40%, 
        #96ceb4 60%, 
        #feca57 80%, 
        #ff9ff3 100%);
    background-size: 300% 300%;
    animation: rainbowShift 6s ease infinite;
    box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.2);
}

/* 样式主题 3: 深色主题 */
.bottom-nav.theme-dark {
    background-color: #1c1c1e;
    border-top: 1px solid #38383a;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.3);
}

/* 样式主题 4: 毛玻璃效果 */
.bottom-nav.theme-glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.1);
}

/* 导航项基础样式 */
.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 100%;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border-radius: 16px;
    margin: 8px 4px;
    backdrop-filter: blur(10px);
}

/* 经典主题导航项 */
.theme-classic .nav-item {
    color: rgba(255, 255, 255, 0.7);
}

.theme-classic .nav-item.active {
    color: #fff;
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    animation: pulse 2s ease-in-out infinite;
}

.theme-classic .nav-item:hover:not(.active) {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* 彩虹主题导航项 */
.theme-rainbow .nav-item {
    color: rgba(255, 255, 255, 0.8);
}

.theme-rainbow .nav-item.active {
    color: #fff;
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.theme-rainbow .nav-item:hover:not(.active) {
    color: #fff;
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

/* 深色主题导航项 */
.theme-dark .nav-item {
    color: #8e8e93;
}

.theme-dark .nav-item.active {
    color: #0a84ff;
    background: rgba(10, 132, 255, 0.1);
    transform: translateY(-2px);
}

.theme-dark .nav-item:hover:not(.active) {
    color: #0a84ff;
    background: rgba(255, 255, 255, 0.05);
}

/* 毛玻璃主题导航项 */
.theme-glass .nav-item {
    color: #666;
}

.theme-glass .nav-item.active {
    color: #007AFF;
    background: rgba(0, 122, 255, 0.1);
    transform: translateY(-2px);
}

.theme-glass .nav-item:hover:not(.active) {
    color: #007AFF;
    background: rgba(0, 122, 255, 0.05);
}

/* 图标样式 */
.nav-icon {
    width: 26px;
    height: 26px;
    margin-bottom: 4px;
    fill: currentColor;
    transition: all 0.3s ease;
}

.nav-item.active .nav-icon {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.theme-rainbow .nav-item.active .nav-icon {
    transform: scale(1.2) rotate(5deg);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* 文字样式 */
.nav-text {
    font-size: 11px;
    line-height: 1;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.nav-item.active .nav-text {
    font-weight: 600;
}

.theme-rainbow .nav-text {
    font-size: 12px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 活跃状态指示器 */
.theme-classic .nav-item.active::after {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #fff, transparent);
    border-radius: 2px;
    opacity: 0.8;
}

.theme-rainbow .nav-item.active::after {
    content: '';
    position: absolute;
    top: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background: linear-gradient(90deg, 
        transparent, 
        #fff 20%, 
        #ff6b6b 40%, 
        #4ecdc4 60%, 
        #fff 80%, 
        transparent);
    border-radius: 2px;
    opacity: 0.9;
    animation: glow 2s ease infinite;
}

.theme-dark .nav-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: #0a84ff;
    border-radius: 1px;
}

.theme-glass .nav-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: #007AFF;
    border-radius: 1px;
}

/* 动画效果 */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes rainbowShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes pulse {
    0% { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); }
    50% { box-shadow: 0 8px 35px rgba(255, 255, 255, 0.2); }
    100% { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); }
}

@keyframes glow {
    0%, 100% { opacity: 0.9; }
    50% { opacity: 0.6; }
}

/* 响应式设计 */
@media (max-width: 480px) {
    body {
        padding-bottom: 65px;
    }
    
    .bottom-nav {
        height: 65px;
    }
    
    .nav-text {
        font-size: 10px;
    }
    
    .nav-icon {
        width: 22px;
        height: 22px;
    }
    
    .nav-item {
        margin: 6px 2px;
        border-radius: 12px;
    }
    
    .theme-rainbow .nav-text {
        font-size: 10px;
    }
    
    .theme-rainbow .nav-icon {
        width: 24px;
        height: 24px;
    }
}

/* 平板设备适配 */
@media (min-width: 768px) and (max-width: 1024px) {
    .bottom-nav {
        height: 75px;
        max-width: 600px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 20px 20px 0 0;
        margin: 0 20px;
    }
    
    body {
        padding-bottom: 95px;
    }
}

/* 桌面设备适配 */
@media (min-width: 1025px) {
    .bottom-nav {
        height: 80px;
        max-width: 500px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 25px 25px 0 0;
        margin: 0 40px;
    }
    
    body {
        padding-bottom: 100px;
    }
    
    .nav-icon {
        width: 28px;
        height: 28px;
    }
    
    .nav-text {
        font-size: 12px;
    }
}
